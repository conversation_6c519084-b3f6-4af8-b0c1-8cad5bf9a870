import tkinter as tk  
from tkinter import filedialog, messagebox  
import pandas as pd  
import logging
import os

# 配置日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class App:  
    def __init__(self, root):  
        self.root = root  
        self.root.title("数据合并工具")  
          
        # 设置按钮  
        self.import_inventory_btn = tk.But<PERSON>(self.root, text="导入库存表格", command=self.import_inventory)  
        self.import_visitors_btn = tk.But<PERSON>(self.root, text="导入访客和买家数", command=self.import_visitors_and_buyers)  
        self.import_personal_statistics_btn = tk.Button(self.root, text="导入七天统计表格", command=self.import_personal_statistics) 
        self.export_btn = tk.Button(self.root, text="合并数据并导出", command=self.merge_and_export)  
          
        # 布局按钮  
        self.import_inventory_btn.pack(side=tk.LEFT, padx=10, pady=10)  
        self.import_visitors_btn.pack(side=tk.LEFT, padx=10, pady=10)  
        self.import_personal_statistics_btn.pack(side=tk.LEFT, padx=10, pady=10)  
        self.export_btn.pack(side=tk.LEFT, padx=10, pady=10)  
          
        # 初始化变量  
        self.inventory_data = None  
        self.visitors_and_buyers_data = []  
        self.personal_statistics =  []   
  
    def import_inventory(self):  
        file_path = filedialog.askopenfilename(title="选择库存表格文件", filetypes=[("Excel files", "*.xls;*.xlsx")])  
        if file_path:  
            self.inventory_data = pd.read_excel(file_path)  
            self.inventory_data['总库存'] = self.inventory_data.groupby('宝贝名称')['规格库存'].transform('sum')  
            self.inventory_data = self.inventory_data.drop_duplicates(subset=['宝贝名称', '宝贝商家编码']).reset_index(drop=True)  
            messagebox.showinfo("成功", "已导入库存表格")  
  
    def preprocess_data(self, df, file_path):
        logging.info(f"A-B 处理文件: {file_path}")
        
        # 打印表格的前10行,以便了解其结构
        logging.info("表格前10行的内容:")
        for i in range(min(10, len(df))):
            logging.info(f"第{i+1}行: {df.iloc[i].tolist()}")
        
        # 读取第4行作为表头
        new_header = df.iloc[3]
        logging.info(f"第4行内容(将作为表头): {new_header.tolist()}")
        
        # 删除前4行(包括表头行),数据从第5行开始
        df = df.iloc[4:].reset_index(drop=True)
        logging.info("B-C 删除表格的1-4行的内容(包括表头行)")
        
        # 设置新表头
        df.columns = new_header
        logging.info(f"设置新表头后的列名: {df.columns.tolist()}")
        logging.info("C-D 第4行的表头内容设置为新表头")
        
        # 将文本类型的数值列转换为数值类型并去除千位分隔符
        for column in df.columns:
            if pd.api.types.is_string_dtype(df[column]) and df[column].str.contains(',', na=False).any():
                df.loc[:, column] = pd.to_numeric(df[column].str.replace(',', ''), errors='coerce')
        logging.info("D-E 遍历所有列,将文本类型的数值列转换为数值类型并去除千位分隔符")
        
        return df
        
    def import_visitors_and_buyers(self):  
        file_paths = filedialog.askopenfilenames(title="选择访客和买家数文件", filetypes=[("Excel files", "*.xls;*.xlsx")])
        if file_paths:
            for index, file_path in enumerate(file_paths, start=1):
                try:
                    df = pd.read_excel(file_path)
                    df = self.preprocess_data(df, file_path)
                    
                    # 检查是否存在我们需要的列
                    required_columns = ['商品名称', '商品访客数', '支付买家数']
                    if all(col in df.columns for col in required_columns):
                        # 修改:同时保留商品ID列,用于后续可能的商家编码替换
                        if '商品ID' in df.columns:
                            df = df[['商品名称', '商品ID', '商品访客数', '支付买家数']]
                        else:
                            df = df[required_columns]
                        
                        df.rename(columns={'商品名称': '宝贝名称'}, inplace=True)
                        self.visitors_and_buyers_data.append(df)
                        logging.info(f"E-F 成功处理第{index}个文件")
                    else:
                        missing_cols = [col for col in required_columns if col not in df.columns]
                        logging.error(f"文件 {file_path} 缺少必要的列: {missing_cols}")
                        logging.error(f"当前列名: {df.columns.tolist()}")
                        messagebox.showerror("错误", f"文件 {os.path.basename(file_path)} 缺少必要的列: {missing_cols}")
                except Exception as e:
                    logging.error(f"处理文件 {file_path} 时出错: {str(e)}")
                    messagebox.showerror("错误", f"处理文件时出错: {str(e)}")
                
            if self.visitors_and_buyers_data:
                messagebox.showinfo("成功", f"已导入 {len(self.visitors_and_buyers_data)} 个访客和买家数文件")

    def import_personal_statistics(self):
        file_paths = filedialog.askopenfilenames(title="选择七天统计表格文件", filetypes=[("Excel files", "*.xls;*.xlsx")])
        if file_paths:
            for index, file_path in enumerate(file_paths, start=1):
                try:
                    df = pd.read_excel(file_path)
                    df = self.preprocess_data(df, file_path)
                    
                    # 检查列名是否存在
                    required_columns = ['商品名称', '商品访客数', '支付买家数']
                    if all(col in df.columns for col in required_columns):
                        # 修改:同时保留商品ID列,用于后续可能的商家编码替换
                        if '商品ID' in df.columns:
                            df = df[['商品名称', '商品ID', '商品访客数', '支付买家数']]
                        else:
                            df = df[required_columns]
                            
                        df.rename(columns={
                            '商品名称': '宝贝名称',
                            '商品访客数': '最近7天商品访客数',
                            '支付买家数': '最近7天支付买家数'
                        }, inplace=True)
                        self.personal_statistics.append(df)
                        logging.info(f"F-G 成功处理第{index}个文件")
                    else:
                        missing_cols = [col for col in required_columns if col not in df.columns]
                        logging.error(f"文件 {file_path} 缺少必要的列: {missing_cols}")
                        logging.error(f"当前列名: {df.columns.tolist()}")
                        messagebox.showerror("错误", f"文件 {os.path.basename(file_path)} 缺少必要的列: {missing_cols}")
                except Exception as e:
                    logging.error(f"处理文件 {file_path} 时出错: {str(e)}")
                    messagebox.showerror("错误", f"处理文件时出错: {str(e)}")
                
            if self.personal_statistics:
                messagebox.showinfo("成功", f"已导入 {len(self.personal_statistics)} 个七天统计表格")

    def merge_and_export(self):
        if not self.visitors_and_buyers_data:
            messagebox.showerror("错误", "请先导入访客买家数文件")
            return
           
        # 合并访客和买家数据 - 不再执行简单去重
        visitors_and_buyers_merged = pd.concat(self.visitors_and_buyers_data, ignore_index=True)
          
        if self.personal_statistics:
            # 合并七天统计数据 - 不再执行简单去重
            personal_statistics_merged = pd.concat(self.personal_statistics, ignore_index=True)
            
            # 将访客买家数据与七天统计数据合并
            if '商品ID' in visitors_and_buyers_merged.columns and '商品ID' in personal_statistics_merged.columns:
                # 如果两个表都包含商品ID,使用宝贝名称和商品ID组合作为合并键
                merged_data = pd.merge(visitors_and_buyers_merged, personal_statistics_merged, 
                                       on=['宝贝名称', '商品ID'], how='outer')
            else:
                # 否则仅使用宝贝名称作为合并键
                merged_data = pd.merge(visitors_and_buyers_merged, personal_statistics_merged, 
                                       on='宝贝名称', how='outer')
        else:
            merged_data = visitors_and_buyers_merged
        
        # 如果存在商品ID列,将其重命名为临时名称,避免与库存表中的宝贝商家编码混淆
        if '商品ID' in merged_data.columns:
            merged_data.rename(columns={'商品ID': '_tmp_商品ID'}, inplace=True)
          
        if self.inventory_data is not None:
            # 将库存数据与之前合并后的数据再次合并
            # 如果访客买家数据中有_tmp_商品ID,则使用宝贝名称和商品ID组合作为合并键
            if '_tmp_商品ID' in merged_data.columns:
                # 先尝试通过宝贝名称和商品ID匹配
                logging.info("使用宝贝名称和商品ID/商家编码组合进行合并")
                # 创建临时列用于合并
                merged_data['_merge_key'] = merged_data['宝贝名称'] + '_' + merged_data['_tmp_商品ID'].astype(str)
                self.inventory_data['_merge_key'] = self.inventory_data['宝贝名称'] + '_' + self.inventory_data['宝贝商家编码'].astype(str)
                
                # 使用_merge_key进行合并
                merged_result = pd.merge(merged_data, self.inventory_data[['_merge_key', '宝贝名称', '宝贝商家编码', '总库存']], 
                                        on='_merge_key', how='outer', suffixes=('', '_inv'))
                
                # 处理合并后的数据
                # 如果宝贝名称_inv不为空,表示在库存数据中找到了匹配项
                # 此时使用库存数据中的宝贝名称和宝贝商家编码
                mask = merged_result['宝贝名称_inv'].notna()
                if '宝贝名称_inv' in merged_result.columns:
                    merged_result.loc[mask, '宝贝名称'] = merged_result.loc[mask, '宝贝名称_inv']
                    merged_result.drop('宝贝名称_inv', axis=1, inplace=True)
                
                # 处理宝贝商家编码
                # 优先使用库存数据中的宝贝商家编码,如果没有则使用_tmp_商品ID
                merged_result['宝贝商家编码'] = merged_result['宝贝商家编码'].fillna(merged_result['_tmp_商品ID'])
                
                # 删除临时列
                merged_result.drop(['_merge_key', '_tmp_商品ID'], axis=1, errors='ignore', inplace=True)
                merged_data = merged_result
            else:
                # 如果没有商品ID,则使用宝贝名称和宝贝商家编码进行合并
                merged_data = pd.merge(merged_data, self.inventory_data[['宝贝名称', '宝贝商家编码', '总库存']], 
                                      on=['宝贝名称'], how='outer')
        
        # 整理最终的数据表格
        columns_to_keep = ['宝贝名称', '宝贝商家编码', '总库存', '商品访客数', '支付买家数', 
                           '最近7天商品访客数', '最近7天支付买家数']
        merged_data = merged_data[[col for col in columns_to_keep if col in merged_data.columns]]
          
        # 确保数值列都是数值类型
        numeric_columns = ['总库存', '商品访客数', '支付买家数', '最近7天商品访客数', '最近7天支付买家数']
        for col in numeric_columns:
            if col in merged_data.columns:
                merged_data[col] = pd.to_numeric(merged_data[col], errors='coerce')
          
        # 只对数值列填充0,非数值列(如宝贝商家编码)保持原样
        for col in numeric_columns:
            if col in merged_data.columns:
                merged_data[col] = merged_data[col].fillna(0)
          
        # 按商品访客数排序  
        try:
            merged_data.sort_values(by='商品访客数', ascending=False, inplace=True)
        except TypeError as e:
            logging.error(f"排序时出错: {str(e)}")
            logging.error(f"商品访客数列的数据类型: {merged_data['商品访客数'].dtype}")
            logging.error(f"商品访客数列的前几个值: {merged_data['商品访客数'].head().tolist()}")
            messagebox.showerror("错误", f"排序时出现问题,可能是数据类型不一致: {str(e)}")
          
        # 去除完全重复的行
        before_dedup = len(merged_data)
        merged_data = merged_data.drop_duplicates()
        after_dedup = len(merged_data)
        if before_dedup > after_dedup:
            logging.info(f"已去除 {before_dedup - after_dedup} 行完全重复的数据")
          
        # 导出最终的表格文件  
        export_file_path = filedialog.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel files", "*.xlsx")])  
        if export_file_path:  
            try:  
                merged_data.to_excel(export_file_path, index=False)  
                messagebox.showinfo("成功", "已导出合并后的表格")  
            except Exception as e:  
                messagebox.showerror("错误", str(e))
        logging.info("所有文件优化后,执行库存数据合并工具的流程")

# 创建主窗口  
root = tk.Tk()  
app = App(root)  
root.mainloop()
